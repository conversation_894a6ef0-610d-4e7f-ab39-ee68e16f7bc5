"""Worker module for background task processing."""
from pathlib import Path
from typing import Any, Optional

from qw_basic_rdb.interface import RelationalDatabase
from qw_basic_s3.interface import S3Storage
from qw_basic_s3.schema import S3BucketDefinition, S3StorageSchema
from qw_config.loader import load_config
from qw_log_interface import NO_LOG_FACTORY, LogFactory
from qw_monodb.table.base import Base
from qw_tenant_config.registry import TenantConfigRegistry
from qw_worker.celery_app import CeleryApp
from qw_worker.config import Qw<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, QwWorkerConfig, QwWorkerMonoConfig
from qw_worker.task_registry import TaskRegistry


class QwWorkerModule(object):
    """Worker module for background task processing.

    This module is responsible for initializing and configuring Celery
    for background task processing.
    """

    def __init__(
        self,
        celery_app: CeleryApp,
        task_registry: TaskRegistry,
        s3_storage: S3Storage,
        rdb: RelationalDatabase,
        tenant_registry: TenantConfigRegistry,
        api_keys: QwWorkerApiKeys,
        lf: LogFactory = NO_LOG_FACTORY,
    ):
        """Initialize the worker module.

        Args:
            celery_app: The Celery application
            task_registry: The task registry
            s3_storage: The S3 storage instance
            rdb: The relational database instance
            tenant_registry: The tenant registry
            api_keys: API keys for external services
            lf: Log factory
        """
        self.celery_app = celery_app
        self.task_registry = task_registry
        self.s3_storage = s3_storage
        self.rdb = rdb
        self.tenant_registry = tenant_registry
        self.api_keys = api_keys
        self.lf = lf

    def get_celery_app(self) -> Any:
        """Get the Celery application.

        Returns:
            The Celery application
        """
        return self.celery_app.app

    @classmethod
    def from_worker_config(
        cls, cfg: QwWorkerConfig, tenant_registry: TenantConfigRegistry, lf: LogFactory = NO_LOG_FACTORY
    ) -> "QwWorkerModule":
        """Create a worker module from a worker configuration.

        Args:
            cfg: The worker configuration
            tenant_registry: The tenant registry
            lf: Log factory

        Returns:
            Initialized worker module
        """
        logger = lf.get_logger(__name__)

        # Create Celery app
        celery_app = CeleryApp(lf=lf)

        # Configure broker
        broker_url: str = (
            f"amqp://{cfg.celery_broker_settings.user}:"
            f"{cfg.celery_broker_settings.password}@"
            f"{cfg.celery_broker_settings.host}:"
            f"{cfg.celery_broker_settings.port}"
        )
        worker_config: dict[str, Any] = {
            "worker_concurrency": cfg.celery_worker_settings.concurrency,
            "worker_max_tasks_per_child": cfg.celery_worker_settings.max_tasks_per_child,
            "worker_log_level": cfg.celery_worker_settings.loglevel,
        }
        celery_app.configure(broker_url, worker_config)

        # Create storage schema with tenant buckets
        storage_schema = S3StorageSchema(
            buckets=[S3BucketDefinition(name=b) for b in tenant_registry.get_tenant_buckets()]
        )

        # Initialize database
        logger.info("Initializing database for worker tasks")
        rdb = cfg.db.build(Base, logger=logger)

        # Initialize S3 storage
        logger.info("Initializing S3 storage for worker tasks")
        s3_storage = cfg.s3.build(storage_schema, lf=lf)

        # Create task registry with required dependencies
        task_registry = TaskRegistry(
            app=celery_app.app,
            s3_storage=s3_storage,
            rdb=rdb,
            tenant_registry=tenant_registry,
            api_keys=cfg.api_keys,
            lf=lf,
        )

        return cls(celery_app, task_registry, s3_storage, rdb, tenant_registry, cfg.api_keys, lf)

    @classmethod
    def from_config(
        cls, config_path: Path, config_overwrite_path: Optional[Path] = None, lf: LogFactory = NO_LOG_FACTORY
    ) -> "QwWorkerModule":
        """Create a worker module from configuration files.

        Args:
            config_path: Path to the main configuration YAML file
            config_overwrite_path: Optional path to configuration overrides
            lf: Log factory to use for logging

        Returns:
            Initialized worker module
        """
        logger = lf.get_logger(__name__)
        logger.info(f"Initializing worker module from config: {config_path}")

        # Load the worker mono configuration
        worker_mono_cfg = load_config(QwWorkerMonoConfig, config_path, config_overwrite_path)

        # Get the worker configuration
        worker_cfg = worker_mono_cfg.worker

        # Create tenant registry
        tenant_registry = TenantConfigRegistry.from_config(worker_cfg.tenants, lf)

        # Create and return the module
        return cls.from_worker_config(worker_cfg, tenant_registry, lf)
