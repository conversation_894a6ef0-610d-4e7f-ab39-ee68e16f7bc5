"""Configuration for FastAPI Agent Service."""
from pathlib import Path
from typing import Optional

from pydantic import BaseModel

from qw_agent_service.config_models import QwAgentServiceMonoConfig
from qw_agent_service.fastapi.authentication.models import AuthConfig
from qw_config.loader import load_config


class AgentServiceConfig(BaseModel):
    """Configuration for the FastAPI agent service."""

    # API Keys
    openai_api_key: str

    # Service Configuration
    runtime_service_url: str = "http://qw-mono-dev-runtime:8000"

    # Authentication Configuration
    auth_config: AuthConfig

    # Feature Flags
    use_fastapi_agent: bool = True
    enable_specialized_agents: bool = False

    # Agent Configuration
    agent_model_name: str = "gpt-4o"
    agent_temperature: float = 0.2
    agent_timeout: float = 30.0

    # Performance Configuration
    max_concurrent_requests: int = 10
    request_timeout: float = 300.0

    @classmethod
    def from_config(cls, qw_mono_config: Path, qw_mono_overwrite_config: Optional[Path] = None) -> "AgentServiceConfig":
        """
        Load configuration from YAML files following qw-mono pattern.
        """
        # Load the agent service mono configuration
        agent_mono_cfg = load_config(QwAgentServiceMonoConfig, qw_mono_config, qw_mono_overwrite_config)

        # Get the agent service configuration
        agent_config = agent_mono_cfg.agent_service

        # Create auth config with application defaults
        auth_config = AuthConfig(
            runtime_service_url=agent_config.runtime_service_url,
            session_validation_timeout=10.0,  # Application default
            session_cache_ttl=300,  # Application default
        )

        return cls(
            openai_api_key=agent_config.api_keys.openai_key,
            runtime_service_url=agent_config.runtime_service_url,
            auth_config=auth_config,
            use_fastapi_agent=True,  # Always true for agent service
            enable_specialized_agents=False,  # Application default
            agent_model_name="gpt-4o",  # Application default
            agent_temperature=0.2,  # Application default
            agent_timeout=30.0,  # Application default
            max_concurrent_requests=10,  # Application default
            request_timeout=300.0,  # Application default
        )
