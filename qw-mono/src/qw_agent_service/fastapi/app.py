"""FastAPI Agent Service - Main Application."""
import os
from contextlib import asynccontextmanager
from typing import AsyncGenerator

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware

from qw_agent_service.agent.agent_service import AgentService
from qw_agent_service.agent.service_provider import ServiceProvider
from qw_agent_service.fastapi.endpoints.internal.agent_health import router as agent_health_router
from qw_agent_service.fastapi.endpoints.internal.server_health import router as server_health_router
from qw_agent_service.fastapi.endpoints.process_prompt import router as process_prompt_router
from qw_log_interface import NO_LOG_FACTORY, LogFactory

from pathlib import Path

from qw_agent_service.config import AgentServiceConfig

# Global instances
agent_service: AgentService
log_factory: LogFactory
app: FastAPI


@asynccontextmanager
async def lifespan(app: FastAPI) -> AsyncGenerator[None, None]:
    """Application lifespan management."""
    global agent_service, log_factory

    # Get config and log factory from app state (set by server.py)
    config = getattr(app.state, "config", None)
    log_factory = getattr(app.state, "log_factory", NO_LOG_FACTORY)

    if config is None:
        # Fallback for direct uvicorn usage (development)

        config_path = Path("dev_data/app.yaml")
        if config_path.exists():
            config = AgentServiceConfig.from_config(config_path)
        else:
            raise RuntimeError("No configuration available and dev_data/app.yaml not found")

    logger = log_factory.get_logger(__name__)

    try:
        logger.info("Starting FastAPI Agent Service...")
        logger.info(f"Configuration loaded - FastAPI agent enabled: {config.use_fastapi_agent}")

        # Initialize service provider (minimal setup for FastAPI)
        service_provider = ServiceProvider(lf=log_factory)

        # Initialize legacy agent service
        agent_service = AgentService(api_key=config.openai_api_key, service_provider=service_provider, lf=log_factory)

        if agent_service.agent_available:
            logger.info("Agent service startup completed successfully")
        else:
            logger.error("Agent service startup failed - agent not available")

        # Store in app state
        app.state.agent_service = agent_service
        app.state.config = config
        app.state.log_factory = log_factory

        yield

    except Exception as e:
        logger.error(f"Startup failed: {e}")
        raise
    finally:
        # Shutdown
        logger.info("Shutting down FastAPI Agent Service...")
        # Legacy agent service doesn't require explicit cleanup
        logger.info("Shutdown completed")


# Create FastAPI application
app = FastAPI(
    title="QW Agent Service",
    description="Dedicated FastAPI service for AI agent processing",
    version="1.0.0",
    lifespan=lifespan,
    docs_url="/docs" if os.getenv("ENVIRONMENT", "development") == "development" else None,
    redoc_url="/redoc" if os.getenv("ENVIRONMENT", "development") == "development" else None,
)

# Add CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=["http://app.docker.localhost", "http://localhost:4200"],
    allow_credentials=True,
    allow_methods=["GET", "POST", "OPTIONS"],
    allow_headers=["*"],
)

app.include_router(process_prompt_router)
app.include_router(agent_health_router)
app.include_router(server_health_router)


if __name__ == "__main__":
    import uvicorn

    uvicorn.run(app, host="0.0.0.0", port=8000, log_level="info")
