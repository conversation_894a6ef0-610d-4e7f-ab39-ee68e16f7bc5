"""Configuration models for the FastAPI Agent Service following qw-mono patterns."""
from pydantic import BaseModel

from qw_basic_rdb.builder.base import RelationalDatabaseConfig
from qw_basic_s3.builder import S3StorageConfig
from qw_log.factory import QwLogConfig
from qw_tenant_config.registry import QwTenantsConfig


class QwAgentServiceApiKeys(BaseModel):
    """API keys for the agent service."""

    openai_key: str


class QwAgentSettings(BaseModel):
    """Agent-specific configuration settings."""

    model_name: str = "gpt-4o"
    temperature: float = 0.2
    timeout: float = 30.0
    enable_specialized_agents: bool = False


class QwPerformanceSettings(BaseModel):
    """Performance-related configuration settings."""

    max_concurrent_requests: int = 10
    request_timeout: float = 300.0


class QwAuthSettings(BaseModel):
    """Authentication-related configuration settings."""

    session_validation_timeout: float = 10.0
    session_cache_ttl: int = 300


class QwAgentServiceBaseConfig(BaseModel):
    """Base configuration for the agent service."""

    runtime_service_url: str
    api_keys: QwAgentServiceApiKeys
    # Optional business logic settings with defaults
    agent_settings: QwAgentSettings = QwAgentSettings()
    performance_settings: QwPerformanceSettings = QwPerformanceSettings()
    auth_settings: QwAuthSettings = QwAuthSettings()


class QwAgentServiceConfig(QwAgentServiceBaseConfig):
    """Agent service configuration.

    This class combines all the configuration needed for the agent service.
    """

    s3: S3StorageConfig
    db: RelationalDatabaseConfig
    tenants: QwTenantsConfig


class QwAgentServiceMonoConfig(BaseModel):
    """Configuration for the agent service in the mono app.

    This class is used to load the configuration from app.yaml.
    """

    mono_agent_service: QwAgentServiceBaseConfig
    mono_s3: S3StorageConfig
    mono_db: RelationalDatabaseConfig
    tenants: QwTenantsConfig
    logging: QwLogConfig

    @property
    def agent_service(self) -> QwAgentServiceConfig:
        """Get the agent service configuration.

        Returns:
            The agent service configuration
        """
        return QwAgentServiceConfig(
            runtime_service_url=self.mono_agent_service.runtime_service_url,
            agent_settings=self.mono_agent_service.agent_settings,
            performance_settings=self.mono_agent_service.performance_settings,
            auth_settings=self.mono_agent_service.auth_settings,
            api_keys=self.mono_agent_service.api_keys,
            s3=self.mono_s3,
            db=self.mono_db,
            tenants=self.tenants,
        )
